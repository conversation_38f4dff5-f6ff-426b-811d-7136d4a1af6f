#include <opencv2/opencv.hpp>
#include <iostream>
#include <vector>
#include <algorithm>

using namespace cv;
using namespace std;

// Structure to store square information
struct SquareInfo {
    vector<Point> contour;
    Point2f center;
    Rect boundingRect;
    Point topLeft, topRight, bottomLeft, bottomRight;
};

// Get four corner points of a contour
void getCornerPoints(const vector<Point>& contour, Point& topLeft, Point& topRight,
                     Point& bottomLeft, Point& bottomRight) {
    // Find bounding rectangle of the contour
    Rect rect = boundingRect(contour);

    // Initialize corner points
    topLeft = Point(rect.x + rect.width, rect.y + rect.height);
    topRight = Point(rect.x, rect.y + rect.height);
    bottomLeft = Point(rect.x + rect.width, rect.y);
    bottomRight = Point(rect.x, rect.y);

    // Iterate through contour points to find the closest points to each corner
    for (const Point& pt : contour) {
        // Top-left: minimum x+y
        if (pt.x + pt.y < topLeft.x + topLeft.y) {
            topLeft = pt;
        }
        // Top-right: maximum x-y
        if (pt.x - pt.y > topRight.x - topRight.y) {
            topRight = pt;
        }
        // Bottom-left: maximum y-x
        if (pt.y - pt.x > bottomLeft.y - bottomLeft.x) {
            bottomLeft = pt;
        }
        // Bottom-right: maximum x+y
        if (pt.x + pt.y > bottomRight.x + bottomRight.y) {
            bottomRight = pt;
        }
    }
}

int main() {
    // Read image (assuming image file name is input.jpg)
    Mat image = imread("input.jpg");
    if (image.empty()) {
        cout << "Cannot read image file" << endl;
        return -1;
    }

    // Convert to grayscale
    Mat gray;
    cvtColor(image, gray, COLOR_BGR2GRAY);

    // Binary threshold to extract white squares
    Mat binary;
    threshold(gray, binary, 200, 255, THRESH_BINARY);

    // Morphological operations to remove noise
    Mat kernel = getStructuringElement(MORPH_RECT, Size(3, 3));
    morphologyEx(binary, binary, MORPH_CLOSE, kernel);
    morphologyEx(binary, binary, MORPH_OPEN, kernel);

    // Find contours
    vector<vector<Point>> contours;
    vector<Vec4i> hierarchy;
    findContours(binary, contours, hierarchy, RETR_EXTERNAL, CHAIN_APPROX_SIMPLE);

    // Filter square contours
    vector<SquareInfo> squares;
    for (size_t i = 0; i < contours.size(); i++) {
        double area = contourArea(contours[i]);

        // Filter out contours with too small area
        if (area < 1000) continue;

        // Calculate approximate polygon of the contour
        vector<Point> approx;
        double epsilon = 0.02 * arcLength(contours[i], true);
        approxPolyDP(contours[i], approx, epsilon, true);

        // Check if it's a quadrilateral (square)
        if (approx.size() == 4) {
            SquareInfo square;
            square.contour = contours[i];
            square.boundingRect = boundingRect(contours[i]);

            // Calculate center point
            Moments m = moments(contours[i]);
            square.center = Point2f(m.m10/m.m00, m.m01/m.m00);

            // Get four corner points
            getCornerPoints(contours[i], square.topLeft, square.topRight,
                           square.bottomLeft, square.bottomRight);

            squares.push_back(square);
        }
    }

    // Ensure 4 squares are found
    if (squares.size() != 4) {
        cout << "Number of detected squares is not 4, actual count: " << squares.size() << endl;
        return -1;
    }

    // Classify squares based on center point positions
    // Sort by y-coordinate to divide into top and bottom rows
    sort(squares.begin(), squares.end(), [](const SquareInfo& a, const SquareInfo& b) {
        return a.center.y < b.center.y;
    });

    // Top two squares (sorted by x-coordinate)
    vector<SquareInfo> topRow(squares.begin(), squares.begin() + 2);
    sort(topRow.begin(), topRow.end(), [](const SquareInfo& a, const SquareInfo& b) {
        return a.center.x < b.center.x;
    });

    // Bottom two squares (sorted by x-coordinate)
    vector<SquareInfo> bottomRow(squares.begin() + 2, squares.end());
    sort(bottomRow.begin(), bottomRow.end(), [](const SquareInfo& a, const SquareInfo& b) {
        return a.center.x < b.center.x;
    });

    // Get target corner points
    Point topLeftCorner = topRow[0].topLeft;        // Top-left corner of top-left square
    Point topRightCorner = topRow[1].topRight;      // Top-right corner of top-right square
    Point bottomLeftCorner = bottomRow[0].bottomLeft;   // Bottom-left corner of bottom-left square
    Point bottomRightCorner = bottomRow[1].bottomRight; // Bottom-right corner of bottom-right square

    // Output results
    cout << "Detected four key corner points:" << endl;
    cout << "Top-left corner of top-left square: (" << topLeftCorner.x << ", " << topLeftCorner.y << ")" << endl;
    cout << "Top-right corner of top-right square: (" << topRightCorner.x << ", " << topRightCorner.y << ")" << endl;
    cout << "Bottom-left corner of bottom-left square: (" << bottomLeftCorner.x << ", " << bottomLeftCorner.y << ")" << endl;
    cout << "Bottom-right corner of bottom-right square: (" << bottomRightCorner.x << ", " << bottomRightCorner.y << ")" << endl;

    // Mark these points on the image (optional, for verification)
    Mat result = image.clone();

    // Draw contours
    for (size_t i = 0; i < 4; i++) {
        drawContours(result, vector<vector<Point>>{squares[i].contour}, -1, Scalar(0, 255, 0), 2);
    }

    // Mark key corner points
    circle(result, topLeftCorner, 8, Scalar(0, 0, 255), -1);      // Red
    circle(result, topRightCorner, 8, Scalar(255, 0, 0), -1);     // Blue
    circle(result, bottomLeftCorner, 8, Scalar(0, 255, 255), -1); // Yellow
    circle(result, bottomRightCorner, 8, Scalar(255, 0, 255), -1); // Magenta

    // Add text labels
    putText(result, "TL", topLeftCorner + Point(10, -10), FONT_HERSHEY_SIMPLEX, 0.7, Scalar(0, 0, 255), 2);
    putText(result, "TR", topRightCorner + Point(10, -10), FONT_HERSHEY_SIMPLEX, 0.7, Scalar(255, 0, 0), 2);
    putText(result, "BL", bottomLeftCorner + Point(10, -10), FONT_HERSHEY_SIMPLEX, 0.7, Scalar(0, 255, 255), 2);
    putText(result, "BR", bottomRightCorner + Point(10, -10), FONT_HERSHEY_SIMPLEX, 0.7, Scalar(255, 0, 255), 2);

    // Display results
    imshow("Original", image);
    imshow("Binary", binary);
    imshow("Result", result);

    waitKey(0);
    destroyAllWindows();

    return 0;
}
