#ifndef HOUGH_LINE_DETECTOR_H
#define HOUGH_LINE_DETECTOR_H

#include <opencv2/opencv.hpp>
#include <vector>


enum EdgeDetectionMethod {
    SOBEL_GRADIENT,      
    LAPLACIAN,           
    MORPHOLOGY_GRADIENT  
};


struct LineSegment {
    float rho;           
    float theta;         
    cv::Point2f start;  
    cv::Point2f end;    
    float strength;     
};


struct DetectedRectangle {
    cv::Point2f topLeft;     
    cv::Point2f topRight;   
    cv::Point2f bottomLeft;  
    cv::Point2f bottomRight; 
    bool isValid;           
};

class HoughLineDetector {
public:

    HoughLineDetector(int threshold = 100, double rho = 1.0, double theta = CV_PI/180,
                     EdgeDetectionMethod method = SOBEL_GRADIENT);

   
    bool detectLines(const cv::Mat& image);

 
    const DetectedRectangle& getRectangle() const { return rectangle_; }
    const std::vector<LineSegment>& getDetectedLines() const { return detected_lines_; }
    const std::vector<LineSegment>& getHorizontalLines() const { return horizontal_lines_; }
    const std::vector<LineSegment>& getVerticalLines() const { return vertical_lines_; }

    // 绘制结果
    cv::Mat drawResults(const cv::Mat& image, bool drawLines = true, bool drawRectangle = true) const;

    // 设置参数
    void setThreshold(int threshold) { threshold_ = threshold; }
    void setRho(double rho) { rho_ = rho; }
    void setTheta(double theta) { theta_ = theta; }
    void setEdgeMethod(EdgeDetectionMethod method) { edge_method_ = method; }

private:
   
    int threshold_;                    
    double rho_;                       
    double theta_;                     
    EdgeDetectionMethod edge_method_;  

  
    cv::Size image_size_;

  
    DetectedRectangle rectangle_;
    std::vector<LineSegment> detected_lines_;
    std::vector<LineSegment> horizontal_lines_;
    std::vector<LineSegment> vertical_lines_;

 
    cv::Mat preprocessImage(const cv::Mat& image);
    cv::Mat detectEdges(const cv::Mat& binary);
    void classifyLines(const std::vector<cv::Vec2f>& lines);
    bool selectBoundaryLines();
    bool validateRectangle();

  
    bool isHorizontal(float theta);
    bool isVertical(float theta);
    cv::Point2f getIntersection(const LineSegment& line1, const LineSegment& line2);
};

#endif // HOUGH_LINE_DETECTOR_H

